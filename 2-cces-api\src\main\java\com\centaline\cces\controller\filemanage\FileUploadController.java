package com.centaline.cces.controller.filemanage;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.centaline.cces.common.annotation.ExcludeWalkingLog;
import com.centaline.cces.common.core.redis.RedisCache;
import com.centaline.cces.common.dto.file.ModelUploadRequest;
import com.centaline.cces.common.dto.file.ModelUploadResponse;
import com.centaline.cces.common.model.driver.ModelAsyncProcessStatus;
import com.centaline.cces.common.model.system.ModelUEditorMedia;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.file.FileUploadService;
import com.centaline.drivermodel.enums.ReturnCode;
import com.centaline.drivermodel.models.ModelMedia;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/fileUpload")
public class FileUploadController {

    @Resource
    FileUploadService serviceFileUpload;

    @Resource
    RedisCache redisCache;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @ExcludeWalkingLog
    public ReturnObject<List<ModelMedia>> upload(@RequestParam(name = "file", required = true) MultipartFile file) throws Exception {
        ReturnObject<List<ModelMedia>> rtn = serviceFileUpload.upload(file,false);
        return rtn;
    }

    /**
     * 上传文件-分片
     */
    @PostMapping("/fragmentUpload")
    @ExcludeWalkingLog
    public ReturnObject<ModelUploadResponse> fragmentUpload(ModelUploadRequest fileRequest) throws Exception {
        ReturnObject<ModelUploadResponse> rtn = serviceFileUpload.fragmentUpload(fileRequest);
        return rtn;
    }

    /**
     * 上传文件-七牛云
     */
    @PostMapping("/uploadToQiNiu")
    @ExcludeWalkingLog
    public ReturnObject<List<ModelMedia>> uploadToQiNiu(@RequestParam(name = "file", required = true) MultipartFile file) throws Exception {
        ReturnObject<List<ModelMedia>> rtn = serviceFileUpload.uploadStreamAuto(file);
        return rtn;
    }


    @PostMapping({"/bigFile/progress"})
    public ReturnObject<ModelAsyncProcessStatus<String>> bigFileProgress(@MultiRequestBody("key") String key, HttpServletRequest request) {
        ReturnObject<ModelAsyncProcessStatus<String>> rtnObj = new ReturnObject<>();

        String val = redisCache.getCacheObject(key);
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        if (ObjectUtil.isNull(val)) {
            asyncProcessResult = new ModelAsyncProcessStatus<>();
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnCode(ReturnCode.Successful);
            asyncProcessResult.setRtnMsg("导出已完成！");
        } else {
            asyncProcessResult = JSONObject.parseObject(val, ModelAsyncProcessStatus.class);
            //暂时不删除，设置了过期时间
//            if (asyncProcessResult.isFlagFinished()) {
//                redisCache.deleteObject(key);
//            }
        }
        rtnObj.setContent(asyncProcessResult);
        rtnObj.setRtnCode(ReturnCode.Successful);
        return rtnObj;
    }


    @PostMapping({"/progress"})
    public ReturnObject<ModelAsyncProcessStatus<String>> progress(@MultiRequestBody("key") String key, HttpServletRequest request) {
        ReturnObject<ModelAsyncProcessStatus<String>> rtnObj = new ReturnObject<>();

        String val = redisCache.getCacheObject(key);
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        if (ObjectUtil.isNull(val)) {
            asyncProcessResult = new ModelAsyncProcessStatus<>();
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnCode(ReturnCode.Successful);
            asyncProcessResult.setRtnMsg("导出已完成！");
        } else {
            asyncProcessResult = JSONObject.parseObject(val, ModelAsyncProcessStatus.class);
            if (asyncProcessResult.isFlagFinished()) {
                redisCache.deleteObject(key);
            }
        }
        rtnObj.setContent(asyncProcessResult);
        //rtnObj.setRtnCode(asyncProcessResult.getRtnCode());
        rtnObj.setRtnCode(ReturnCode.Successful);
        // rtnObj.setRtnMsg(asyncProcessResult.getRtnMsg());
        return rtnObj;
    }

    /**
     * 富文本中的附件上传
     */
    @PostMapping("/uploadRichText")
    @ExcludeWalkingLog
    public ModelUEditorMedia uploadRichText(@RequestParam(name = "file", required = true) MultipartFile file) throws Exception {
        ModelUEditorMedia rtn = serviceFileUpload.uploadRichText(file);
        return rtn;
    }

    /**
     * 上传录音
     */
    @PostMapping("/fragmentUploadTempCustomerVoice")
    @ExcludeWalkingLog
    public ReturnObject<ModelUploadResponse> fragmentUploadTempCustomerVoice(ModelUploadRequest fileRequest) {
        return serviceFileUpload.fragmentUploadTempCustomerVoice(fileRequest);
    }
}
