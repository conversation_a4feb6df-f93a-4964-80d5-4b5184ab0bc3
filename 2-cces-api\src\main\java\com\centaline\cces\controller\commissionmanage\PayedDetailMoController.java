package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.service.commissionmanage.PayedDetailMoService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 实收明细Mo宝
 *
 * <AUTHOR>
 * @since 2023-3-27
 */
@RestController
@RequestMapping("/commissionmanage/PayedDetailMo")
@Api(tags = "实收明细")
public class PayedDetailMoController {

    @Resource
    PayedDetailMoService listService;

    @PostMapping({"/getLayoutOfSearch"})
    @ApiOperation(value = "获取筛选项", notes = "获取筛选项")
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {
        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    @ApiOperation(value = "获取列表模型", notes = "获取列表模型")
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {
        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

}
