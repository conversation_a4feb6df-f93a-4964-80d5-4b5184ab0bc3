package com.centaline.cces.controller.appmanage;

import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanage.DailyReportService;
import com.centaline.cces.service.estatemanager.BuildingService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/app/DailyReport")
public class DailyReportController {
    @Resource
    DailyReportService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("dailyReportId") String dailyReportId,
            @MultiRequestBody("estateId") String estateId,
            @MultiRequestBody("estateName") String estateName) {
        return service.readDetail(actionType, dailyReportId,estateId,estateName);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.save(actionType, formData);
    }

    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @MultiRequestBody("dailyReportId") String dailyReportId) {
        return service.delete(dailyReportId);
    }
}
