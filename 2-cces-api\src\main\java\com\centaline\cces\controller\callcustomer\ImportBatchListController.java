package com.centaline.cces.controller.callcustomer;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.service.callcustomer.ImportBatchListService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
@RestController
@RequestMapping("/callcustomer/importbatchlist")
@Api(tags = "导入批次管理列表")
public class ImportBatchListController {
    @Resource
    ImportBatchListService listService;

    @ApiOperation(value = "获取筛选项", notes = "获取筛选项")
    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }
    @ApiOperation(value = "获取列表模型", notes = "获取列表模型")
    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {

        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }
}
