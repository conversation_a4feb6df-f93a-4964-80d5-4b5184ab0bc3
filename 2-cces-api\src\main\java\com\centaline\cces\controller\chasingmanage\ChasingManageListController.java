package com.centaline.cces.controller.chasingmanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.chasingmanage.ChasingManageListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/chasingmanage/chasingmanagelist")
public class ChasingManageListController {

    @Resource
    ChasingManageListService listService;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;

    @PostMapping({"/getLayoutOfSearch"})
    @ApiOperation(value = "获取筛选项", notes = "获取筛选项")
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    @ApiOperation(value = "获取列表模型", notes = "获取列表模型")
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {
        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }
    @PostMapping({"/exportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcel(@RequestBody RequestAsyncExcel requestAsyncExcel) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);
        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, listService);        //异步处理文档

        return rtn;
    }
}
