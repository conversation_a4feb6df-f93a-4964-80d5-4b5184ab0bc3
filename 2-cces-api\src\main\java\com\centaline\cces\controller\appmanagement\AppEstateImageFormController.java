package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.utils.StringUtils;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.AppEstateImageFormService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 *
 * <AUTHOR>
 * @since 2024/09/05 14:35
 */
@RestController
@RequestMapping("/appmanagement/AppEstateImageForm")
public class AppEstateImageFormController {

    @Resource
    AppEstateImageFormService formService;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("estateRoomTypeId") String keyId) {
        return formService.readDetail(actionType, keyId);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return formService.save(actionType, formData);
    }

    @PostMapping({"/deleteBatch"})
    public ReturnObject<Object> deleteBatch(
            @MultiRequestBody("optParamInfo") String keyIds) {

        return formService.delete(keyIds);
    }
    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @MultiRequestBody("optParamInfo") String keyId) {
        if (StringUtils.isNotEmpty(keyId)) {
            keyId = "[\"" + keyId + "\"]";
        }
        return formService.delete(keyId);
    }
    @PostMapping({"/saveUpBatch"})
    public ReturnObject<Object> saveUpBatch(
            @MultiRequestBody("optParamInfo") String keyIds) {

        return formService.saveUp(keyIds);
    }
}
