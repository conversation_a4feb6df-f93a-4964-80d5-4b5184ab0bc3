package com.centaline.cces.controller.baddebtsalemanage;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.baddebtsalemanage.BadDebtRecordService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 实收明细财务确认-列表
 *
 * <AUTHOR>
 * @since 2022/03/03 10:16
 */
@RestController
@RequestMapping("/salemanage_baddebt/baddebtrecord")
public class BadDebtRecordController {
    @Resource
    BadDebtRecordService service;

    @PostMapping({"/approve"})
    public ReturnObject<Object> approve(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("badRecordId") String badRecordIds, @MultiRequestBody("adjustId") String adjustIds) {


        ModelParamFormData modelParam = new ModelParamFormData();
        modelParam.addField("badRecordIds", badRecordIds);
        modelParam.addField("adjustIds", adjustIds);

        return service.save(ActionType.getItem(1), modelParam);
    }
    @PostMapping({"/approveremark"})
    public ReturnObject<Object> approveremark(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.save(ActionType.getItem(1), formData);
    }
    @PostMapping({"/refuse"})
    public ReturnObject<Object> refuse(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


//        ModelParamFormData modelParam=new ModelParamFormData();
//        modelParam.addField("badRecordIds",badRecordIds);
//        modelParam.addField("adjustIds",adjustIds);
//        modelParam.addField("operateRemark",operateRemark);

        return service.save(ActionType.getItem(0), formData);
    }
}
