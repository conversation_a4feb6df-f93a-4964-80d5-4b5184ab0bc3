package com.centaline.cces.controller.cache;

import com.centaline.cces.common.core.redis.RedisCache;
import com.centaline.cces.service.cache.ServiceCacheDriverForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 缓存
 *
 * <AUTHOR>
 * @since 2021/12/2 9:09
 */
@RestController
@RequestMapping("/cache")
public class CacheController {
    @Resource
    ServiceCacheDriverForm cacheDriverFormService;

    @Resource
    RedisCache redisCache;

    @PostMapping("/driver")
    public ReturnObject<Object> cacheDriverForm() {
        cacheDriverFormService.cacheDriverForm();
        return new ReturnObject<>();
    }

}
