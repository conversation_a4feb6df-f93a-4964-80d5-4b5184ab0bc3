package com.centaline.cces.controller.appmanage;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.service.appmanage.MyListService;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/app/My")
public class MyListController {

    @Resource
    MyListService listService;

    @PostMapping({"/getMyMenuList"})
    public ReturnObject<ModelSearchPage<Object>> getMyMenuList() {

        return listService.getMyMenuList();
    }


}
