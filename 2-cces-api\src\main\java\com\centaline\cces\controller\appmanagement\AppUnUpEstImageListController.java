package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.AppUnUpEstImageListService;
import com.centaline.drivermodel.models.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目业绩目标
 */
@RestController
@RequestMapping("/appmanagement/AppUnUpEstImageList")
public class AppUnUpEstImageListController {
    @Resource
    AppUnUpEstImageListService listService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject
            , @MultiRequestBody("searchFields") SearchFields sf) {
        return listService.getLayoutOfSearch(sf);
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject, @RequestBody ModelParamQueryData paramDataQuery) {
        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/getFile"})
    public ReturnObject<List<ModelMedia>> getFile(
            @MultiRequestBody("roomTypeName") String roomTypeName
            , @MultiRequestBody("filePath") String mediaUrl) {

        return listService.getFile(roomTypeName, mediaUrl);
    }

}
