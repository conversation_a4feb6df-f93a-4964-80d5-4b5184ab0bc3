package com.centaline.cces.controller.bookingmanage;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.bookingmanage.BookingService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/bookingmanage/booking")
public class BookingController {

    @Resource
    BookingService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("bookingId") String bookingId
    ) {
        return service.readDetail(ActionType.New, bookingId);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.save(actionType, formData);
    }

    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @MultiRequestBody("bookingId") String bookingId) {

        return service.delete(bookingId);
    }

    @PostMapping({"/getCustomerInfo"})
    public ReturnObject<Object> getCustomerInfo(
            @MultiRequestBody("customerId") String customerId) {
        return service.getCustomerInfo(customerId);
    }
}
