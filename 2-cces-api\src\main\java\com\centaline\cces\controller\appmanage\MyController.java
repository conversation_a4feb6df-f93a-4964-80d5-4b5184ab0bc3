package com.centaline.cces.controller.appmanage;

import com.centaline.cces.common.dto.systemmanage.ContactsDTO;
import com.centaline.cces.common.dto.systemmanage.FeedbackInfoDTO;
import com.centaline.cces.common.dto.systemmanage.FeedbackInfoRequestDTO;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanage.MyService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/app/My")
public class MyController {

    @Resource
    MyService service;

    @PostMapping({"/readDetailByUpdateMobile"})
    public ReturnObject<ModelForm> readDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("empId") String empId) {
        return service.readDetailByUpdateMobile(actionType, empId);
    }

    @PostMapping({"/saveMobile"})
    public ReturnObject<Object> saveMobile(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {

        return service.saveMobile(actionType, formData);
    }

    @PostMapping({"/getSetUrls"})
    public ReturnObject<Map<String, String>> getSetUrls(
            @RequestHeader("authObject") AuthObject authObject) {
        return service.getSetUrls(authObject);
    }
    //获取我的反馈
    @GetMapping({"/getMyFeedbackInfo"})
    public ReturnObject<FeedbackInfoDTO> getMyFeedbackInfo() {
        return service.getMyFeedbackInfo();
    }

    @PostMapping({"/saveFeedbackInfo"})
    public ReturnObject<Object> saveFeedbackInfo(@RequestBody FeedbackInfoRequestDTO feedbackInfo) {
        return service.saveFeedbackInfo(feedbackInfo);
    }

}
