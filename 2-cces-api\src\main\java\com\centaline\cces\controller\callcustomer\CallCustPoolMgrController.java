package com.centaline.cces.controller.callcustomer;

import cn.hutool.core.util.ObjectUtil;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.callcustomer.CallCustPoolMgrService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import com.centaline.drivermodel.models.SearchFields;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Call客池管理
 */
@RestController
@RequestMapping("/callcustomer/CallCustPoolMgr")
public class CallCustPoolMgrController {

    @Resource
    CallCustPoolMgrService cmdService;

    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @MultiRequestBody("customerID") String [] keyIds) {

        return cmdService.delete(keyIds);
    }

    @PostMapping({"/deleteAll"})
    public ReturnObject<Object> deleteAll(
            @RequestBody ModelParamQueryData paramDataQuery) {

        return cmdService.deleteAll(paramDataQuery);
    }
}
