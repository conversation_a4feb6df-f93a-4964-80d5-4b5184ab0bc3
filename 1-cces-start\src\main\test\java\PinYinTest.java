import cn.hutool.extra.pinyin.PinyinUtil;
import com.centaline.cces.CcesApplication;
import com.centaline.cces.service.job.CheckContAndAdjustExceptionDataJobService;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试枚举
 *
 * <AUTHOR>
 * @since 2021/12/21 13:44
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CcesApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j

public class PinYinTest {
    @Resource
    CheckContAndAdjustExceptionDataJobService checkContAndAdjustExceptionDataJobService;

    private static final Map<String, String> multiPinyinMap =  new HashMap<>();

    static {
        // 示例：为“重”字指定在“重庆”这个词组中的拼音
        multiPinyinMap.put("重庆", "chong");
        multiPinyinMap.put("蔚蓝", "wei");
        // 添加更多的多音字和对应上下文的拼音
    }

    @Test
    public void test111() {
//        checkContAndAdjustExceptionDataJobService.checkFinActualCommissionDetailSnapshotJobHandler();
        System.out.println(getFirstLetter("珈誉玖玺C1z."));
        System.out.println(getFirstLetter("蔚蓝左岸花园"));
        System.out.println(getFirstLetter("蔚"));
        System.out.println(getFirstLetter("重"));
        System.out.println(getFirstLetter("重庆"));
        System.out.println(getFirstLetter("重工"));


        System.out.println(PinyinUtil.getFirstLetter("珈誉玖玺C1z.",""));
        System.out.println(PinyinUtil.getFirstLetter("蔚蓝左岸花园",""));
        System.out.println(PinyinUtil.getFirstLetter("蔚",""));
        System.out.println(PinyinUtil.getFirstLetter("重",""));
        System.out.println(PinyinUtil.getFirstLetter("重庆",""));
        System.out.println(PinyinUtil.getFirstLetter("重工",""));
//
//        System.out.println(ChineseFirstPY.getSpells(("重庆")));
//        System.out.println(ChineseFirstPY.getSpells(("重工")));
//        System.out.println(getFirstLettersWithContext(("重庆")));
//        System.out.println(getFirstLettersWithContext(("重工")));
//
//        System.out.println(getFirstLettersWithContext(("珈誉玖玺C1z.")));
//        System.out.println(getFirstLettersWithContext(("蔚蓝左岸花园")));
//        System.out.println(getFirstLettersWithContext(("蔚县")));
//        System.out.println(getFirstLettersWithContext(("他重做了这个重要的工作")));
//        System.out.println(getFirstLettersWithContext(("行万里路")));


    }
    /**
     * 获取拼音首字母（根据上下文处理多音字）
     * cache项目中有源码
     */
//    public static String getFirstLettersWithContext(String text) {
//        List<Pinyin> pinyinList = HanLP.convertToPinyinList(text);
//        return pinyinList.stream()
//                .map(pinyin -> pinyin.getPinyinWithoutTone().substring(0, 1))
//                .collect(Collectors.joining());
//    }
//

    /**
     * 获取汉字字符串的拼音首字母
     *
     * @param text 汉字字符串
     * @return 拼音首字母字符串
     */
    public static String getFirstLetter(String text) {
        StringBuilder sb = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            try {
                // 检查是否为汉字
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null) {
                        // 检查多音字词典
                        String contextKey = getContextKey(text, i);
                        String pinyin = multiPinyinMap.getOrDefault(contextKey, pinyinArray[0]);
                        sb.append(pinyin.charAt(0));
                    }
                } else {
                    // 非汉字直接追加
                    sb.append(c);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return sb.toString();
    }

    /**
     * 获取当前字符的上下文键
     *
     * @param text 全文
     * @param index 当前字符索引
     * @return 上下文键
     */
    private static String getContextKey(String text, int index) {
        // 这里简单实现，根据需要可以扩展上下文逻辑
        // 例如，可以取当前字符前后各n个字符作为上下文
        return text.substring(index, Math.min(index + 2, text.length()));
    }


    public static String getFirstLetters1(String chinese) {
        StringBuilder result = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        for (char c : chinese.toCharArray()) {
            // 处理汉字
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]")) {
                String[] pinyinArray = new String[0];
                try {
                    pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    throw new RuntimeException(e);
                }
                if (pinyinArray != null && pinyinArray.length > 0) {
                    // 取第一个拼音的首字母
                    result.append(pinyinArray[0].charAt(0));
                }
            } else {
                // 处理非汉字字符：字母转为大写，其他字符可忽略
                if (Character.isLetter(c)) {
                    result.append(Character.toUpperCase(c));
                }
                // 可选：保留数字或符号
                // else if (Character.isDigit(c) || ...) { ... }
            }
        }
        return result.toString();
    }
}
