package com.centaline.cces.controller.appmanage;

import cn.hutool.core.bean.BeanUtil;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.common.model.driver.ModelSearchPageEx;
import com.centaline.cces.service.appmanage.DailyReportListService;
import com.centaline.cces.service.estatemanager.BuildingListService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/app/DailyReportList")
public class DailyReportListController {

    @Resource
    DailyReportListService listService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch() {
        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {
        ModelSearchPageEx<Object> ex = new ModelSearchPageEx<>();
        ReturnObject<ModelSearchPage<Object>> searchPage = listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
        BeanUtil.copyProperties(searchPage.getContent(),ex);
        searchPage.setContent(ex);
        return searchPage;
    }

}
