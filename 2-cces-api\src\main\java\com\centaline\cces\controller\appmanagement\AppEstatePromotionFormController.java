package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.integratedmanage.AppEstatePromotionFormService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 公司资讯
 *
 * <AUTHOR>
 * @since 2024/08/28 14:35
 */
@RestController
@RequestMapping("/appmanagement/AppEstatePromotionForm")
public class AppEstatePromotionFormController {

    @Resource
    AppEstatePromotionFormService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("estateId") String keyId) {
        return service.readDetail(actionType, keyId);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.save(actionType, formData);
    }

}
