package com.centaline.cces.controller.appmanage;

import com.centaline.cces.common.annotation.ExcludeWalkingLog;
import com.centaline.cces.common.dto.contractmanage.ContractEntryDTO;
import com.centaline.cces.common.dto.systemmanage.CompanyActiveMesDTO;
import com.centaline.cces.common.dto.systemmanage.HomeMenuDTO;
import com.centaline.cces.common.dto.systemmanage.MenuUnreadQuantityDTO;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanage.HomeMenuService;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/HomeMenuList")
public class HomeMenuListController {

    @Resource
    HomeMenuService listService;

    @GetMapping({"/getList"})
    public ReturnObject<List<HomeMenuDTO>> getList(@RequestHeader("authObject") AuthObject authObject) {
        return listService.getList(authObject);
    }

    @GetMapping({"/getOperationBtn"})
    @ExcludeWalkingLog
    public ReturnObject<String> getOperationBtn(@RequestHeader("authObject") AuthObject authObject) {

        return listService.getOperationBtn(authObject);
    }

    @PostMapping({"/getContractEntryUrl"})
    @ExcludeWalkingLog
    public ReturnObject<ModelSearchPage<Object>> getContractEntryUrl(@RequestHeader("authObject") AuthObject authObject,
                                                                     @MultiRequestBody("contractEntry") ContractEntryDTO contractEntry) {

        return listService.getContractEntryUrl(authObject, contractEntry);
    }

    @GetMapping({"/getMenuUnreadQuantity"})
    @ExcludeWalkingLog
    public ReturnObject<List<MenuUnreadQuantityDTO>> getMenuUnreadQuantity() {

        return listService.getMenuUnreadQuantity();
    }

    @PostMapping({"/getCompanyActiveMesList"})
    @ExcludeWalkingLog
    public ReturnObject<List<CompanyActiveMesDTO>> getCompanyActiveMesList(@RequestHeader("authObject") AuthObject authObject,
                                                                           @MultiRequestBody("activeEmpType") String activeEmpType) {

        return listService.getCompanyActiveMesList(authObject, activeEmpType);
    }

    /*
    销售经理信息采集
    * */
    @PostMapping({"/getCollectEstateInfoUrl"})
    @ExcludeWalkingLog
    public ReturnObject<String> getCollectEstateInfoUrl() {

        return listService.getCollectEstateInfoUrl();
    }

    /*
    总监预报数
    * */
    @PostMapping({"/getReportDataUrl"})
    @ExcludeWalkingLog
    public ReturnObject<String> getReportDataUrl() {

        return listService.getReportDataUrl();
    }

}
