package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.core.redis.RedisCache;
import com.centaline.cces.common.dto.estatemanagement.EstateDTO;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelAsyncProcessStatus;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.AppEstateListService;
import com.centaline.cces.service.estatemanager.EstateListService;
import com.centaline.cces.service.estatemanager.EstateMoListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.enums.SearchDataType;
import com.centaline.drivermodel.enums.SearchOperation;
import com.centaline.drivermodel.models.*;
import com.centaline.publics.models.PageAttribute;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/appmanagement/appestatelist")
public class AppEstateListController {
    @Resource
    AppEstateListService service;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask task;

    @Resource
    RedisCache redisCache;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch() {
        return service.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {
        return service.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/getListById"})
    public ReturnObject<Map<String,Object>> getListById(@MultiRequestBody("estateId") String  estateId) {
        return service.getListById(estateId);
    }

    @PostMapping({"/asyncExportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcel(@RequestBody RequestAsyncExcel requestAsyncExcel) {

        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        redisCache.setCacheObject(exportAsyncRes.getKey(), com.alibaba.fastjson.JSONObject.toJSONString(new ModelAsyncProcessStatus<>()));
        task.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, service);

        return rtn;
    }

}
