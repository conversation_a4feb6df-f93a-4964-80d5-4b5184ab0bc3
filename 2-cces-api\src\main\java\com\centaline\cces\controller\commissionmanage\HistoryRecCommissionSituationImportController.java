package com.centaline.cces.controller.commissionmanage;

import cn.hutool.core.util.StrUtil;
import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.enums.common.EnumStaticExcelTemplate;
import com.centaline.cces.common.exception.BaseException;
import com.centaline.cces.common.model.base.ImportExcelModel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.utils.poi.ExcelUtil;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.commissionmanage.HistoryRecCommissionSituationImportService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelRow;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/commissionmanage/HistoryRecCommissionSituationImport")
public class HistoryRecCommissionSituationImportController {

    @Resource
    HistoryRecCommissionSituationImportService service;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;

    @PostMapping({"/saveImportDataToBusiness"})
    public ReturnObject<Object> saveImportDataToBusiness(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return service.saveImportDataToBusiness(actionType, formData);
    }

    @GetMapping({"/exportStaticExcelTemplate"})
    public void exportStaticExcelTemplate() {
        ExcelUtil.exportStaticExcelTemplate(EnumStaticExcelTemplate.历史对数开票标识导入);
    }

    @PostMapping({"/importExcelData"})
    public ReturnObject importExcelData(
            @RequestParam(name = "file") MultipartFile file) throws Exception {

        return service.importExcelData(file);

    }

    @PostMapping({"/getLayoutOfNew"})
    public ReturnObject<ModelForm> getLayoutOfNew(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType) {


        return service.getLayoutOfNew(actionType);
    }

    /**
     * 获取上传之后的数据
     *
     * @param importExcelModel
     * @return
     */
    @PostMapping({"/getImportDataList"})
    public ReturnObject<List<ModelRow>> getImportDataList(
            @MultiRequestBody ImportExcelModel importExcelModel) {
        return service.getImportDataList(importExcelModel);
    }

    /**
     * @param authObject
     * @param importExcelModel
     * @return
     */
    @PostMapping({"/asyncExportErrorData"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportErrorData(@RequestHeader("authObject") AuthObject authObject,
                                                                        @MultiRequestBody ImportExcelModel importExcelModel) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();


        if (StrUtil.isBlankIfStr(importExcelModel.getImportId())) {
            throw new BaseException("系统没有检测到导入的数据！");
        }

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        RequestAsyncExcel requestAsyncExcelDTO = new RequestAsyncExcel().setImportID(importExcelModel.getImportId());
        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcelDTO, service);        //异步处理文档

        return rtn;
    }

}
