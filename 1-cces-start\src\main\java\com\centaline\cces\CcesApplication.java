package com.centaline.cces;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class, DruidDataSourceAutoConfigure.class, MybatisAutoConfiguration.class})
@EnableApolloConfig
@EnableScheduling
@EnableAsync
public class CcesApplication {
    /**
     * 服务启动时间
     */
    public static LocalDateTime appStartTime;
    public static LocalDateTime appReadyTime;
    static Logger logger = LoggerFactory.getLogger(CcesApplication.class);

    public static void main(String[] args) throws UnknownHostException {
        appStartTime = LocalDateTime.now();

        // System.setProperty("spring.devtools.restart.enabled", "false");
//        SpringApplication.run(CcesApplication.class, args);

//        SpringApplication application = new SpringApplication(CcesApplication.class);
//        application.addListeners(new PropertiesListener("application.properties"));
//        application.run(args);

        ConfigurableApplicationContext application = SpringApplication.run(CcesApplication.class, args);

        appReadyTime = LocalDateTime.now();
        Environment env = application.getEnvironment();
        String host = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");

        System.out.println("(♥◠‿◠)ﾉﾞ  CCES   ლ(´ڡ`ლ)ﾞ          \n" +
                " .__________.   .__________.   .__________." + "    .__________.   " + "              " + ".__.      .__.     \n" +
                " |  ._______|   |  ._______|   |  ._______|" + "   /   ______.  \\  " + "              " + "|  |      |  |     \n" +
                " |  |           |  |           |  |        " + "   |  |       \\_/  " + "              " + "|  |      |  |     \n" +
                " |  |           |  |           |  |_______." + "   |  (_______.    " + ".__________.  " + "|  |      |  |     \n" +
                " |  |           |  |           |  ._______|" + "   (._______.  \\  " + " |__________|  " + "|  |      |  |     \n" +
                " |  |           |  |           |  |        " + "             \\  \\   " + "             " + "|  |      |  |     \n" +
                " |  |_______.   |  |_______.   |  |_______." + "   /`\\_______ﾉ  /  " + "              " + "|  `.____.`  |     \n" +
                " |__________|   |__________|   |__________|" + "   \\___________ﾉ   " + "              " + "`.__________.`     \n");

        logger.info("\n----------------------------------------------------------\n\t" +
                        "Application [{}:{}] is running! Access URLs:\n\t" +
                        "Local: \t\thttp://localhost:{}{}\n\t" +
                        "External: \thttp://{}:{}{}\n\t" +
                        "doc: \thttp://{}:{}{}/doc.html\n\t" +
                        "----------------------------------------------------------",
                env.getProperty("cces.name"),env.getActiveProfiles()[0],
                env.getProperty("server.port"),env.getProperty("server.servlet.context-path"),
                host, port,env.getProperty("server.servlet.context-path"),
                host, port,env.getProperty("server.servlet.context-path"));
    }
}
