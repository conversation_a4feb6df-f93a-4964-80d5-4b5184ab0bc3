package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.service.commissionmanage.PayedDetailService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 实收明细
 *
 * <AUTHOR>
 * @since 2022/06/26 14:35
 */
@RestController
@RequestMapping("/commissionmanage/PayedDetail")
public class PayedDetailController {

    @Resource
    PayedDetailService listService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {


        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

}
