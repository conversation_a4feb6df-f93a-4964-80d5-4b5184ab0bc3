package com.centaline.cces.controller.baddebtsalemanage;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.baddebtsalemanage.BadDebtRecordRefuseFormService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 成交录入人转移
 *
 * <AUTHOR>
 * @since 2022/06/26 14:35
 */
@RestController
@RequestMapping("/salemanage/baddebtrecordrefuseform")
public class BadDebtRecordRefuseFormController {

    @Resource
    BadDebtRecordRefuseFormService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("badRecordId") String badRecordIds, @MultiRequestBody("adjustId") String adjustIds) {


        return service.readDetailExtend(actionType, badRecordIds, adjustIds);
    }
    @PostMapping({"/readDetailAuditRemark"})
    public ReturnObject<ModelForm> readDetailAuditRemark(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("badRecordId") String badRecordIds, @MultiRequestBody("adjustId") String adjustIds) {


        return service.readDetailAuditRemark(actionType, badRecordIds, adjustIds);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return service.save(actionType, formData);
    }

    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("uuid") String uuid) {


        return service.delete(uuid);
    }
}
