package com.centaline.cces.controller.chasingmanage;

import cn.hutool.core.util.StrUtil;
import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.enums.common.EnumExcelType;
import com.centaline.cces.common.enums.common.EnumStaticExcelTemplate;
import com.centaline.cces.common.exception.BaseException;
import com.centaline.cces.common.exception.CustomException;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.utils.file.FileUploadUtils;
import com.centaline.cces.common.utils.poi.ExcelUtil;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.chasingmanage.RiskManageClaimCommissionFollowImportService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;


/**
 * 追佣反馈导入
 */
@RestController
@RequestMapping("/chasingmanage/riskmanageclaimcommissionfollowimport")
public class RiskManageClaimCommissionFollowImportController {
    @Resource
    RiskManageClaimCommissionFollowImportService service;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;

    @PostMapping({"/saveImportDataToBusiness"})
    public ReturnObject<Object> saveImportDataToBusiness(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return service.saveImportDataToBusiness(actionType, formData);
    }

    //下载模板
    @GetMapping({"/getImportTemplate"})
    public void exportStaticExcelTemplate() {
        ExcelUtil.exportStaticExcelTemplate(EnumStaticExcelTemplate.风险管理部追佣反馈导入);
    }

    //导入
    @PostMapping({"/importExcelData"})
    public ReturnObject importExcelData(
            @RequestParam(name = "file") MultipartFile file) throws Exception {
        RequestAsyncExcel requestAsyncExcel = new RequestAsyncExcel();
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        //检查文件类型
        String type = FileUploadUtils.getExtension(file);
        if (!(EnumExcelType.xls.getCode().equals(type) || EnumExcelType.xlsx.getCode().equals(type))) {
            throw new CustomException("请选择Excel文件类型！");
        }
        //1、缓存key
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);
        requestAsyncExcel.setRedisKey(exportAsyncRes.getKey());
        service.importExcelData(file.getBytes(),requestAsyncExcel);
        return rtn;
    }

    @PostMapping({"/getLayoutOfNew"})
    public ReturnObject<ModelForm> getLayoutOfNew(
            @MultiRequestBody("actionType") ActionType actionType) {


        return service.getLayoutOfNew(actionType);
    }

    /**
     * 获取上传之后的数据
     *
     * @param batchId
     * @return
     */
    @PostMapping({"/getImportDataList"})
    public ReturnObject<List<ModelRow>> getImportDataList(
            @MultiRequestBody("batchId") String batchId) {
        return service.getImportDataList(batchId);
    }

    /**
     * @param batchId
     * @return
     */
    @PostMapping({"/asyncExportErrorData"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportErrorData(@MultiRequestBody("batchId") String batchId) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        if (StrUtil.isBlankIfStr(batchId)) {
            throw new BaseException("系统没有检测到导入的数据！");
        }

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        RequestAsyncExcel requestAsyncExcelDTO = new RequestAsyncExcel().setImportID(batchId);
        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcelDTO, service);        //异步处理文档

        return rtn;
    }


}
