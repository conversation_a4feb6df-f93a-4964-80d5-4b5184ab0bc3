package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.AppEstateDynamicListService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 项目业绩目标
 */
@RestController
@RequestMapping("/appmanagement/AppEstateDynamicList")
public class AppEstateDynamicListController {
    @Resource
    AppEstateDynamicListService appEstateDynamicListService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(
            @RequestHeader("authObject") AuthObject authObject
            , @MultiRequestBody("estateId") String estateId
            , @MultiRequestBody("estateName") String estateName) {
        return appEstateDynamicListService.getLayoutOfSearch(estateId, estateName);
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject, @RequestBody ModelParamQueryData paramDataQuery) {


        return appEstateDynamicListService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }


}
