package com.centaline.cces.controller.baddebtsalemanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.baddebtsalemanage.BadDebtRecordListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 实收明细财务确认-列表
 *
 * <AUTHOR>
 * @since 2022/03/03 10:16
 */
@RestController
@RequestMapping("/salemanage_baddebt/baddebtrecordlist")
@Api(tags = "坏账记录列表")
public class BadDebtRecordListController {
    @Resource
    BadDebtRecordListService listService;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask task;

    @ApiOperation(value = "获取筛选项", notes = "获取筛选项")
    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch() {


        return listService.getLayoutOfSearch();
    }

    @ApiOperation(value = "获取列表模型", notes = "获取列表模型")
    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {

        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/exportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> exportExcel(@RequestBody RequestAsyncExcel requestAsyncExcel) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes);
        rtn.setNotification(ActionType.None);
        //异步处理文档
        task.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, listService);

        return rtn;
    }

}
