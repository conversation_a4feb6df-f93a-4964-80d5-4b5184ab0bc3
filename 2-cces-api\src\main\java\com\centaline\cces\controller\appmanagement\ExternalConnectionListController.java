package com.centaline.cces.controller.appmanagement;
import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.core.redis.RedisCache;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelAsyncProcessStatus;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.appmanagement.ExternalConnectionListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/appmanagement/externalconnectionlist")
@Api(tags = "外网接线员管理列表")
public class ExternalConnectionListController {
    @Resource
    ExternalConnectionListService listService;
    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;

    @ApiOperation(value = "获取筛选项", notes = "获取筛选项")
    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }
    @ApiOperation(value = "获取列表模型", notes = "获取列表模型")
    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {

        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/exportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcel(@RequestBody RequestAsyncExcel requestAsyncExcel) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, listService);        //异步处理文档

        return rtn;
    }
}
