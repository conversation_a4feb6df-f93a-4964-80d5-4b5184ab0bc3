package com.centaline.cces.controller.appmanage;

import cn.hutool.core.bean.BeanUtil;
import com.centaline.cces.common.dto.estatemanagement.EstateAgentListDTO;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.common.model.driver.ModelSearchPageEx;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanage.PlanComMonthListService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/app/plancommonthlist")
public class PlanComMonthListController {

    @Resource
    PlanComMonthListService listService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch() {
        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {
        ModelSearchPageEx<Object> ex = new ModelSearchPageEx<>();
        ReturnObject<ModelSearchPage<Object>> searchPage = listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
        BeanUtil.copyProperties(searchPage.getContent(),ex);
        searchPage.setContent(ex);
        return searchPage;
    }

    @PostMapping({"/getEstateAgentID"})
    public ReturnObject<EstateAgentListDTO> getEstateAgentID(
            @MultiRequestBody("estateID") String estateID) {
        return listService.getEstateAgentID(estateID);
    }
}
