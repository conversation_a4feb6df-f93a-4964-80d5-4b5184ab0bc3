package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.dto.estatemanagement.CommonOperationAuthorityDTO;
import com.centaline.cces.common.dto.file.AnnexReviewTranInfoDTO;
import com.centaline.cces.common.dto.file.FileListDTO;
import com.centaline.cces.common.dto.file.FileVerifyDTO;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.commissionmanage.UploadDevComService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import com.centaline.drivermodel.models.SearchFields;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/commissionmanage/uploadDevCom")
public class UploadDevComController {
    @Resource
    UploadDevComService uploadDevComService;

    @PostMapping({"/getAddFileDetail"})
    public ReturnObject<ModelForm> getAddFileDetail(@RequestHeader("authObject") AuthObject authObject,
                                                    @MultiRequestBody("actionType") ActionType actionType,
                                                    @MultiRequestBody("commId") String commId,
                                                    @MultiRequestBody("estateId") String estateId) {
        return uploadDevComService.getAddFileDetail(actionType, commId,estateId);
    }


    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {

        return uploadDevComService.save(actionType, formData);
    }



}
