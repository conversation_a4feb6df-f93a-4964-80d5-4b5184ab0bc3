#Apollo 配置
app:
  id: CCESMO                            #应用ID
apollo:
  env: ${APOLLO__ENV:Dev}
  cacheDir: /opt/data/                       #配置本地配置缓存目录
  cluster: ${APOLLO__CLUSTER:develop}                           #指定使用哪个集群的配置
  meta: ${APOLLO__METASERVER:http://dev-meta-apollo.centaline.com.cn}           #DEV环境配置中心地址
  autoUpdateInjectedSpringProperties: true   #是否开启 Spring 参数自动更新
  bootstrap:
    enabled: true                            #是否开启 Apollo
    namespaces: ${APOLLO__NAMESPACES:application,dbconnection,sysparameter}                #设置 Namespace
    eagerLoad:
      enabled: true                         #将 Apollo 加载提到初始化日志系统之前
