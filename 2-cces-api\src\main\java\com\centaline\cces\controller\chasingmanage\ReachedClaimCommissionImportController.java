package com.centaline.cces.controller.chasingmanage;

import cn.hutool.core.util.StrUtil;
import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.enums.common.EnumStaticExcelTemplate;
import com.centaline.cces.common.exception.BaseException;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.utils.poi.ExcelUtil;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.chasingmanage.ReachedClaimCommissionImportService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelRow;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;


/**
 * 已达未收款原因导入
 */
@RestController
@RequestMapping("/chasingmanage/reachedclaimcommissionimport")
public class ReachedClaimCommissionImportController {
    @Resource
    ReachedClaimCommissionImportService service;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;



    @PostMapping({"/saveImportDataToBusiness"})
    public ReturnObject<Object> saveImportDataToBusiness(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {

        return service.saveImportDataToBusiness(actionType, formData);
    }

    //下载模板
    @GetMapping({"/getImportTemplate"})
    public void getImportTemplate() {
        ExcelUtil.exportStaticExcelTemplate(EnumStaticExcelTemplate.已达未收款原因导入模板);
    }

    //导入
    @PostMapping({"/importExcelData"})
    public ReturnObject importExcelData(
            @RequestParam(name = "file") MultipartFile file
            ,@RequestParam(name = "type") String type) throws Exception {

        return service.importExcelData(file,type);

    }

    @PostMapping({"/getLayoutOfNew"})
    public ReturnObject<ModelForm> getLayoutOfNew(
            @MultiRequestBody("type") String type) {

        return service.getLayoutOfNew(type);
    }

    /**
     * 获取上传之后的数据
     *
     * @param batchId
     * @return
     */
    @PostMapping({"/getImportDataList"})
    public ReturnObject<List<ModelRow>> getImportDataList(
            @MultiRequestBody("batchId") String batchId) {
        return service.getImportDataList(batchId);
    }

//    @PostMapping({"/getImportTotalList"})
//    public ReturnObject<List<CallCustPoolImportTotalDTO>> getImportTotalList(
//            @MultiRequestBody("batchId") String batchId) {
//        return service.getImportTotalList(batchId);
//    }

    /**
     * @param batchId
     * @return
     */
    @PostMapping({"/asyncExportErrorData"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportErrorData(@MultiRequestBody("batchId") String batchId) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        if (StrUtil.isBlankIfStr(batchId)) {
            throw new BaseException("系统没有检测到导入的数据！");
        }

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        RequestAsyncExcel requestAsyncExcelDTO = new RequestAsyncExcel().setImportID(batchId);
        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcelDTO, service);        //异步处理文档

        return rtn;
    }

}
