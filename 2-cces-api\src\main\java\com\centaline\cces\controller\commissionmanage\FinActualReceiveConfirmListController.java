package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.commissionmanage.FinActualReceiveConfirmExportService;
import com.centaline.cces.service.commissionmanage.FinActualReceiveConfirmListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 实收明细财务确认-列表
 *
 * <AUTHOR>
 * @since 2022/03/03 10:16
 */
@RestController
@RequestMapping("/commissionmanage/finactualreceiveconfirmlist")
public class FinActualReceiveConfirmListController {
    @Resource
    FinActualReceiveConfirmListService listService;

    @Resource
    FinActualReceiveConfirmExportService exportService;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {


        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/asyncExportExcelMain"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcelMain(@RequestHeader("authObject") AuthObject authObject,
                                                                        @MultiRequestBody RequestAsyncExcel requestAsyncExcelDTO) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();


        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcelDTO, listService);        //异步处理文档

        return rtn;
    }

    @PostMapping({"/asyncExportExcelItem"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcelItem(@RequestBody RequestAsyncExcel requestAsyncExcel,
                                                                        @MultiRequestBody("commId") String commId,
                                                                        @MultiRequestBody("type") String type) {
        exportService.setExportTypeAndKey(requestAsyncExcel, type, commId);
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, exportService);        //异步处理文档

        return rtn;
    }

    @PostMapping({"/asyncExportExcelForCD"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcelForCD(@RequestHeader("authObject") AuthObject authObject,
                                                                        @MultiRequestBody("commId") String commId) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();


        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        RequestAsyncExcel requestAsyncExcelDTO = new RequestAsyncExcel().setKeyId(commId);
        requestAsyncExcelDTO.setType("CD");
        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcelDTO, listService);        //异步处理文档

        return rtn;
    }

}
