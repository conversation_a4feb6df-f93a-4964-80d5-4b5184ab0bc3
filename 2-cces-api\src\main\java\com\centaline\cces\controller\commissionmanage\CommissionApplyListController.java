package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.commissionmanage.CommissionApplyListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/commissionmanage/CommissionApplyList")
public class CommissionApplyListController {

    @Resource
    CommissionApplyListService listService;

    @Resource
    ExcelAsyncTask task;

    @Resource
    CacheUtil cacheUtil;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {


        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    /*明细导出*/
    @PostMapping({"/exportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> exportExcel(@RequestBody RequestAsyncExcel requestAsyncExcel,
                                                               @MultiRequestBody("commId") String commId,
                                                               @MultiRequestBody("type") String type) {
        listService.setExportTypeAndKey(requestAsyncExcel, type, commId);
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        task.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, listService);        //异步处理文档

        return rtn;
    }


}
