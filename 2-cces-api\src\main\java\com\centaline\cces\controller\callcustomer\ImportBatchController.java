package com.centaline.cces.controller.callcustomer;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.callcustomer.ImportBatchService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/callcustomer/importbatch")
@Api(tags = "导入批次管理列表-复打")
public class ImportBatchController {

    @Resource
    ImportBatchService service;

    @PostMapping({"/copybatch"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("importID") String importID) {

        return service.copybatch(actionType, importID);
    }
}
