package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.core.redis.RedisCache;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelAsyncProcessStatus;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.appmanagement.EstateUpDownRecordListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 项目上下架记录-列表
 */
@RestController
@RequestMapping("/appmanagement/estateupdownrecordlist")
public class EstateUpDownRecordListController {
    @Resource
    EstateUpDownRecordListService listService;

    @Resource
    ExcelAsyncTask task;

    @Resource
    RedisCache redisCache;

    @Resource
    CacheUtil cacheUtil;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch() {
        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {
        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/asyncExportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcel(@RequestBody RequestAsyncExcel requestAsyncExcel) {

        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        redisCache.setCacheObject(exportAsyncRes.getKey(), com.alibaba.fastjson.JSONObject.toJSONString(new ModelAsyncProcessStatus<>()));
        task.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, listService);

        return rtn;
    }

}
