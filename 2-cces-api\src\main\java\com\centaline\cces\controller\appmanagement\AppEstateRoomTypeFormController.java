package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.utils.StringUtils;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.AppEstateRoomTypeFormService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 *
 * <AUTHOR>
 * @since 2024/09/05 14:35
 */
@RestController
@RequestMapping("/appmanagement/AppEstateRoomTypeForm")
public class AppEstateRoomTypeFormController {

    @Resource
    AppEstateRoomTypeFormService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("estateRoomTypeId") String keyId) {
        return service.readDetail(actionType, keyId);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.save(actionType, formData);
    }

    @PostMapping({"/deleteBatch"})
    public ReturnObject<Object> deleteBatch(
            @MultiRequestBody("estateRoomTypeId") String keyIds) {

        return service.delete(keyIds);
    }
    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @MultiRequestBody("estateRoomTypeId") String keyId) {
        if (StringUtils.isNotEmpty(keyId)) {
            keyId = "[\"" + keyId + "\"]";
        }
        return service.delete(keyId);
    }
    @PostMapping({"/saveUpBatch"})
    public ReturnObject<Object> saveUpBatch(
            @MultiRequestBody("estateIdAndRoomTypeId") String keyIds) {

        return service.saveUp(keyIds);
    }
}
