package com.centaline.cces.controller.callcustomer;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.enums.common.EnumExcelType;
import com.centaline.cces.common.enums.common.EnumStaticExcelTemplate;
import com.centaline.cces.common.exception.CustomException;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.common.utils.JsonUtils;
import com.centaline.cces.common.utils.file.FileUploadUtils;
import com.centaline.cces.common.utils.poi.ExcelUtil;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.callcustomer.CallCustPoolImportService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ReturnObject;
import com.centaline.drivermodel.models.SearchField;
import com.centaline.drivermodel.models.SearchFields;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;


/**
 * Call客池导入
 */
@RestController
@RequestMapping("/callcustomer/callcustpoolimport")
public class CallCustPoolImportController {
    @Resource
    CallCustPoolImportService service;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;

    @PostMapping({"/saveImportDataToBusiness"})
    public ReturnObject<Object> saveImportDataToBusiness(@RequestBody ModelParamQueryData paramDataQuery) {

        return service.save(paramDataQuery);
    }

    //下载模板
    @GetMapping({"/getImportTemplate"})
    public void getImportTemplate() {
        ExcelUtil.exportStaticExcelTemplate(EnumStaticExcelTemplate.洗客池导入模版顶部);
    }

    //导入
    @PostMapping({"/importExcelData"})
    public ReturnObject<ModelAsyncProcessResponse> importExcelData(
            @RequestParam(name = "file") MultipartFile file
            ,@RequestParam(name = "fields") String fields) throws Exception {
        SearchFields sf;
        List<SearchField> sfs;
        RequestAsyncExcel requestAsyncExcel = new RequestAsyncExcel();
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        //检查文件类型
        String type = FileUploadUtils.getExtension(file);
        if (!(EnumExcelType.xls.getCode().equals(type) || EnumExcelType.xlsx.getCode().equals(type))) {
            throw new CustomException("请选择Excel文件类型！");
        }
        //1、缓存key
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);
        //2、处理参数
        sfs = JsonUtils.parseArray(fields, SearchField.class);
        sf = new SearchFields(sfs);
        requestAsyncExcel.setRedisKey(exportAsyncRes.getKey());
        requestAsyncExcel.setType(sf.getFirstSearchItemV1("type"));
        requestAsyncExcel.setBatchId(sf.getFirstSearchItemV1("batchId"));
        service.importExcelData(file.getBytes(),requestAsyncExcel);
        return rtn;
    }


}
