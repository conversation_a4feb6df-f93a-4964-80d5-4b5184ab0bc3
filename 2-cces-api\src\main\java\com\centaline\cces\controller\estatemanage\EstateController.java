package com.centaline.cces.controller.estatemanage;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.utils.StringUtils;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.estatemanager.EstateService;
import com.centaline.cces.service.interfaces.systemmanage.ISysMenuService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/12/7 11:02
 * @Description:
 */

@RestController
@RequestMapping("/estatemanage/estate")
public class EstateController {

    @Resource
    EstateService estateService;
    @Resource
    private ISysMenuService menuService;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("estateId") String estateId) {


        return estateService.readDetail(actionType, estateId);
    }

    @PostMapping({"/getEstateInfo"})
    public ReturnObject<ModelForm> getEstateInfo() {

        return estateService.getEstateInfo();
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return estateService.save(actionType, formData);
    }

    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("estateId") String estateId) {


        return estateService.delete(estateId);
    }

    @PostMapping({"/copyEstateDetail"})
    public ReturnObject<ModelForm> copyEstateDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("estateId") String estateId) {


        return estateService.copyEstateDetail(estateId);
    }

    @PostMapping({"/saveCopyEstate"})
    public ReturnObject<Object> saveCopyEstate(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return estateService.saveCopyEstate(formData);
    }

    @PostMapping({"/audit"})
    public ReturnObject<Object> audit(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("estateId") String estateId,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        if(formData!=null&& StringUtils.isBlank(estateId))
        {
            estateId=formData.getPropertyStringValue("estateId");
        }

        return estateService.audit(estateId);
    }

    @PostMapping({"/backDetail"})
    public ReturnObject<ModelForm> backDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("estateId") String estateId,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        if(formData!=null&& StringUtils.isBlank(estateId))
        {
            estateId=formData.getPropertyStringValue("estateId");
        }

        return estateService.backDetail(estateId);
    }

    @PostMapping({"/savebackDetail"})
    public ReturnObject<Object> savebackDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return estateService.savebackDetail(formData);
    }

}
