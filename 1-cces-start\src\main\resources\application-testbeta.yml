# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9999
  servlet:
    # 应用的访问路径
    context-path: /service-api
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    # Tomcat启动初始化的线程数，默认值25
    threads:
      max: 800
      min-spare: 30
    max-http-form-post-size: 35MB

# 日志配置
logging:
  level:
    com.centaline.cces: debug
    org.springframework: warn
    com.centaline.dao: debug
    org.springframework.security.authentication: debug

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  #返回过滤null属性
  jackson:
    default-property-inclusion: non_null
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 80MB
      # 设置总上传的文件大小
      max-request-size: 80MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # mvc模式
  mvc:
    static-path-pattern: /static/**
    view:
      prefix: /views/
      suffix: .html
  thymeleaf:
    #缓冲的配置
    cache: false
    #开启MVC thymeleaf 视图解析
    enabled: true
    encoding: UTF-8
    mode: LEGACYHTML5
    prefix: classpath:/views/
    suffix: .html
  data:
    mongodb:
      uri: mongodb://***********/CCES3_HistoryData



# PageHelper分页插件
pagehelper:
  helperDialect: sqlserver
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /
  # 分组
  group:
    groupName: 'tool' #工具类分组
    key: ''
    basePackage: 'com.centaline.cces.controller.tool'



# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


