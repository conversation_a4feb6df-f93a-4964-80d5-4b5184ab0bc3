package com.centaline.cces.controller.appmanagement;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.ExternalConnectionService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
@RestController
@RequestMapping("/appmanagement/externalconnection")
public class ExternalConnectionController {
    @Resource
    ExternalConnectionService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("centaOperatorID") String centaOperatorID) {
        return service.readDetail(actionType, centaOperatorID);
    }


    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.save(actionType, formData);
    }
    @PostMapping({"/saveordnum"})
    public ReturnObject<Object> saveordnum(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.saveordnum(actionType, formData);
    }
    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @MultiRequestBody("centaOperatorID") String centaOperatorIDs) {
        return service.delete(centaOperatorIDs);
    }

    @PostMapping({"/readDetailForEmployee"})
    public ReturnObject<ModelForm> readDetailForEmployee(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("centaOperatorID") String centaOperatorID) {
        return service.readDetailForEmployee(actionType, centaOperatorID);
    }

    @PostMapping({"/saveForEmployee"})
    public ReturnObject<Object> saveForEmployee(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.saveForEmployee(actionType, formData);
    }
}
