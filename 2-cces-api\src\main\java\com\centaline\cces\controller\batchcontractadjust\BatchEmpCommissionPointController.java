package com.centaline.cces.controller.batchcontractadjust;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.batchcontractadjust.BatchEmpCommissionPointService;
import com.centaline.cces.service.salemanage.ContractConsultantBatchAdjustService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/salemanage/batchempcommissionpoint")
public class BatchEmpCommissionPointController {

    @Resource
    BatchEmpCommissionPointService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("ccBatchId") String ccBatchId) {
        return service.readDetail(actionType, ccBatchId);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return service.save(actionType, formData);
    }

    @PostMapping({"/getAdjustLayoutOfSearch"})
    @ApiOperation(value = "获取筛选项", notes = "获取筛选项")
    public ReturnObject<ModelForm> getAdjustLayoutOfSearch(@MultiRequestBody("agentId") String agentId,
                                                           @MultiRequestBody("empId") String empId) {


        return service.getAdjustLayoutOfSearch(agentId,empId);
    }

    @PostMapping({"/getSelectBatchAdjustList"})
    @ApiOperation(value = "获取列表模型", notes = "获取列表模型")
    public ReturnObject<ModelSearchPage<Object>> getSelectBatchAdjustList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {

        return service.getSelectBatchAdjustList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }
    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("ccbatchID") String ccbatchID) {

        return service.delete(ccbatchID);
    }

}
