package com.centaline.cces.controller.filemanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.enums.common.EnumStaticExcelTemplate;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.utils.poi.ExcelUtil;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.necessaryfile.ContractConfirmImportService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelRow;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/necessaryfile/ContractConfirmImport")
public class ContractConfirmImportController {
    @Resource
    ContractConfirmImportService service;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask task;

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {


        return service.save(actionType, formData);
    }

    @PostMapping({"/delete"})
    public ReturnObject<Object> delete(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("buildingID") String buildingID) {


        return service.delete(buildingID);
    }

    @GetMapping({"/exportStaticExcelTemplate"})
    public void exportStaticExcelTemplate() {
        ExcelUtil.exportStaticExcelTemplate(EnumStaticExcelTemplate.成交确认清单导入模版);
    }

    @PostMapping({"/genExcelTemplate"})
    public ReturnObject<ModelAsyncProcessResponse> genExcelOtherTemplate(@RequestHeader("authObject") AuthObject authObject,
                                                                         @MultiRequestBody("actionType") ActionType actionType,
                                                                         @RequestBody RequestAsyncExcel uploadDTO) {

        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes);
        rtn.setNotification(ActionType.None);
        //异步处理文档
        task.exportExcel(exportAsyncRes.getKey(), uploadDTO, service);

        return rtn;
    }

    @PostMapping({"/importData"})
    public ReturnObject importData(
            @RequestParam(name = "file") MultipartFile file,
            @RequestParam(name = "agentId") String agentId) throws Exception {

        return service.importData(file, agentId);

    }

    @PostMapping({"/getContractConfirmImportForm"})
    public ReturnObject<ModelForm> readDetail(
            @RequestHeader("authObject") AuthObject authObject,
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("importID") String importID) {


        return service.readDetail(actionType, importID);
    }

    /**
     * 获取上传之后的数据
     *
     * @param importId
     * @return
     */
    @PostMapping({"/getContractConfirmImportList"})
    public ReturnObject<List<ModelRow>> getContractConfirmImportList(
            @MultiRequestBody("ImportID") String importId) {
        return service.getContractConfirmImportList(importId);
    }


}
