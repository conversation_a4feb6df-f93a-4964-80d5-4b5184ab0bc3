package com.centaline.cces.controller.chasingmanage;

import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.chasingmanage.ChasingManageService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/chasingmanage/chasingmanage")
public class ChasingManageController {

    @Resource
    ChasingManageService service;

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {
        return service.save( actionType,formData);
    }

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("contractId") String contractId,
            @MultiRequestBody("adjustId") String adjustId) {
        return service.readDetail(actionType, contractId,adjustId);
    }


}
