package com.centaline.cces.controller.bookingmanage;

import cn.hutool.core.bean.BeanUtil;
import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.common.model.driver.ModelSearchPageEx;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.bookingmanage.BookingListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 成交交单明细(成都)
 */

@RestController
@RequestMapping("/bookingmanage/BookingList")
public class BookingListController {
    @Resource
    BookingListService listService;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask asyncTask;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch() {

        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {
        ModelSearchPageEx<Object> ex = new ModelSearchPageEx<>();
        ReturnObject<ModelSearchPage<Object>> searchPage = listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
        BeanUtil.copyProperties(searchPage.getContent(),ex);
        searchPage.setContent(ex);
        return searchPage;
    }

    @PostMapping({"/exportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcel(@RequestBody RequestAsyncExcel requestAsyncExcelDTO,
                                                                    @MultiRequestBody("exportType") String exportType) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();

        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);

        //根据exportType判断导出类型
        listService.setExcelId(exportType, requestAsyncExcelDTO.getSearchFields());

        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcelDTO, listService);        //异步处理文档

        return rtn;
    }


}
