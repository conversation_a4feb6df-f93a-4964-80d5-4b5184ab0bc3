package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.AppEstateRoomTypeListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelMedia;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目业绩目标
 */
@RestController
@RequestMapping("/appmanagement/AppEstateRoomTypeList")
public class AppEstateRoomTypeListController {
    @Resource
    AppEstateRoomTypeListService appEstateRoomTypeListService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject
            , @MultiRequestBody("estateId") String estateId
            , @MultiRequestBody("estateName") String estateName) {
        return appEstateRoomTypeListService.getLayoutOfSearch(estateId,estateName);
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject, @RequestBody ModelParamQueryData paramDataQuery) {
        return appEstateRoomTypeListService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/getFile"})
    public ReturnObject<List<ModelMedia>> getFile(
            @MultiRequestBody("roomTypeName") String roomTypeName
            , @MultiRequestBody("filePath") String mediaUrl) {

        return appEstateRoomTypeListService.getFile(roomTypeName, mediaUrl);
    }

}
