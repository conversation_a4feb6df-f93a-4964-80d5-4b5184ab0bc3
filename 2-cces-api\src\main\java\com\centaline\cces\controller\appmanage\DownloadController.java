package com.centaline.cces.controller.appmanage;

import com.centaline.cces.common.dto.systemmanage.AppDownloadUrlDTO;
import com.centaline.cces.common.dto.systemmanage.HomeMenuDTO;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.service.appmanage.DownloadService;
import com.centaline.cces.service.appmanage.HomeMenuService;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/app/Download")
public class DownloadController {

    @Resource
    DownloadService downloadService;

    @GetMapping({"/getAppDownloadUrl"})
    public ReturnObject<AppDownloadUrlDTO> getAppDownloadUrl() {

        return downloadService.getAppDownloadUrl();
    }

}
