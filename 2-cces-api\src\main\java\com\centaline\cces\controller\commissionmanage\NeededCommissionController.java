package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.commissionmanage.NeededCommissionService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/commissionmanage/NeededCommission")
public class NeededCommissionController {

    @Resource
    NeededCommissionService listService;
    @Resource
    CacheUtil cacheUtil;
    @Resource
    ExcelAsyncTask asyncTask;


    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch() {


        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {

        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }


    @PostMapping({"/exportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcel(@RequestBody RequestAsyncExcel requestAsyncExcelDTO) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes).setNotification(ActionType.None);
        asyncTask.exportExcel(exportAsyncRes.getKey(), requestAsyncExcelDTO, listService);        //异步处理文档
        return rtn;
    }
}
