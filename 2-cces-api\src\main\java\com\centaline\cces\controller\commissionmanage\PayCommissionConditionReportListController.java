package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.commissionmanage.PayCommissionConditionReportListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 结佣条件项报表
 *
 * <AUTHOR>
 * @since 2021/12/1 15:00
 */
@RestController
@RequestMapping("/commissionmanage/payconditionreport")
public class PayCommissionConditionReportListController {
    @Resource
    PayCommissionConditionReportListService payCommissionConditionReportListService;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask task;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return payCommissionConditionReportListService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {


        return payCommissionConditionReportListService.getList(paramDataQuery.getSearchFields(),
                paramDataQuery.getPageAttribute(),
                paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/exportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> exportExcel(@RequestBody RequestAsyncExcel requestAsyncExcel) {

        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes);
        rtn.setNotification(ActionType.None);
        //异步处理文档
        task.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, payCommissionConditionReportListService);

        return rtn;
    }


}
