package com.centaline.cces.controller.appmanage;

import com.centaline.cces.common.dto.appmanage.AppReportDTO;
import com.centaline.cces.common.dto.systemmanage.AppDownloadUrlDTO;
import com.centaline.cces.common.dto.systemmanage.HomeMenuDTO;
import com.centaline.cces.service.appmanage.AppReportService;
import com.centaline.cces.service.appmanage.DownloadService;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/app/AppReport")
public class AppReportController {
    @Resource
    AppReportService appReportService;

    @GetMapping({"/getReportList"})
    public ReturnObject<List<HomeMenuDTO>> getReportList() {
        return appReportService.getReportList();
    }
}
