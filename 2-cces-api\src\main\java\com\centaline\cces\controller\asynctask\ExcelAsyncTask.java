package com.centaline.cces.controller.asynctask;

import com.centaline.cces.common.annotation.ExcludeWalkingLog;
import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.base.ImportExcelModel;
import com.centaline.cces.common.model.driver.ModelAsyncProcessStatus;
import com.centaline.cces.service.base.IServiceAsyncDownFile;
import com.centaline.cces.service.base.IServiceAsyncExportExcel;
import com.centaline.cces.service.base.IServiceAsyncExportSecondExcel;
import com.centaline.cces.service.base.IServiceAsyncImportExcel;
import com.centaline.drivermodel.enums.ReturnCode;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Excel异步任务
 *
 * <AUTHOR>
 * @since 2022/5/7 17:25
 */
@Component
@ExcludeWalkingLog
public class ExcelAsyncTask {

    @Resource
    CacheUtil cacheUtil;

    @Async
    public void exportExcel(String key, RequestAsyncExcel requestAsyncExcelDTO, IServiceAsyncExportExcel serviceAsyncExportExcel) {
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        try {
            requestAsyncExcelDTO.setRedisKey(key);
            asyncProcessResult = serviceAsyncExportExcel.asyncExportExcel(requestAsyncExcelDTO);

        } catch (Exception exception) {
            exception.printStackTrace();
            asyncProcessResult.setRtnCode(ReturnCode.Error);
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnMsg("文件处理失败：" + exception.getMessage());
            asyncProcessResult.setContent("文件处理失败：" + exception.getMessage());
        }
        if(key.contains("big")) {
            cacheUtil.cacheBigAsyncProcessStatus(key, asyncProcessResult);
        }else {
            cacheUtil.cacheAsyncProcessStatus(key, asyncProcessResult);
        }
    }

    @Async
    public void asyncExportExcelTemplate(String key, ImportExcelModel importExcelModel, IServiceAsyncImportExcel serviceAsyncImportExcel) {
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        try {
            asyncProcessResult = serviceAsyncImportExcel.asyncExportExcelTemplate(importExcelModel);

        } catch (Exception exception) {
            exception.printStackTrace();
            asyncProcessResult.setRtnCode(ReturnCode.Error);
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnMsg("文件处理失败：" + exception.getMessage());
            asyncProcessResult.setContent("文件处理失败：" + exception.getMessage());
        }
        cacheUtil.cacheAsyncProcessStatus(key, asyncProcessResult);
    }


    @Async
    public void exportExcelSecond1(String key, RequestAsyncExcel requestAsyncExcelDTO, IServiceAsyncExportSecondExcel serviceAsyncExportExcel) {
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        try {
            asyncProcessResult = serviceAsyncExportExcel.exportExcelSecond1(requestAsyncExcelDTO);

        } catch (Exception exception) {
            exception.printStackTrace();
            asyncProcessResult.setRtnCode(ReturnCode.Error);
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnMsg("文件处理失败：" + exception.getMessage());
            asyncProcessResult.setContent("文件处理失败：" + exception.getMessage());
        }
        cacheUtil.cacheAsyncProcessStatus(key, asyncProcessResult);
    }


    @Async
    public void exportExcelSecond2(String key, RequestAsyncExcel requestAsyncExcelDTO, IServiceAsyncExportSecondExcel serviceAsyncExportExcel) {
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        try {
            asyncProcessResult = serviceAsyncExportExcel.exportExcelSecond2(requestAsyncExcelDTO);

        } catch (Exception exception) {
            exception.printStackTrace();
            asyncProcessResult.setRtnCode(ReturnCode.Error);
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnMsg("文件处理失败：" + exception.getMessage());
            asyncProcessResult.setContent("文件处理失败：" + exception.getMessage());
        }
        cacheUtil.cacheAsyncProcessStatus(key, asyncProcessResult);
    }


    @Async
    public void exportExcelSecond3(String key, RequestAsyncExcel requestAsyncExcelDTO, IServiceAsyncExportSecondExcel serviceAsyncExportExcel) {
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        try {
            asyncProcessResult = serviceAsyncExportExcel.exportExcelSecond3(requestAsyncExcelDTO);

        } catch (Exception exception) {
            exception.printStackTrace();
            asyncProcessResult.setRtnCode(ReturnCode.Error);
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnMsg("文件处理失败：" + exception.getMessage());
            asyncProcessResult.setContent("文件处理失败：" + exception.getMessage());
        }
        cacheUtil.cacheAsyncProcessStatus(key, asyncProcessResult);
    }


    @Async
    public void asyncDownFile(String key, RequestAsyncExcel requestAsyncExcelDTO, IServiceAsyncDownFile serviceAsyncDownFile) {
        ModelAsyncProcessStatus<String> asyncProcessResult = new ModelAsyncProcessStatus<>();
        try {
            requestAsyncExcelDTO.setRedisKey(key);
            asyncProcessResult = serviceAsyncDownFile.asyncDownFile(requestAsyncExcelDTO);

        } catch (Exception exception) {
            exception.printStackTrace();
            asyncProcessResult.setRtnCode(ReturnCode.Error);
            asyncProcessResult.setPercentage(100);
            asyncProcessResult.setFlagFinished(true);
            asyncProcessResult.setRtnMsg("文件处理失败：" + exception.getMessage());
            asyncProcessResult.setContent("文件处理失败：" + exception.getMessage());
        }
        cacheUtil.cacheAsyncProcessStatus(key, asyncProcessResult);
    }

}
