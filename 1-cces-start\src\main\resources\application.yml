#启动配置
spring:
  profiles:
    active: ${ACTIVE__NAME:dev,devapollo}




#Apollo 配置
#app:
#  id: CCESMO                            #应用ID
#apollo:
#  env: ${Apollo__Env:Dev}
#  cacheDir: /opt/data/                       #配置本地配置缓存目录
#  cluster: ${Apollo__Cluster:develop}                           #指定使用哪个集群的配置
#  meta: ${Apollo__MetaServer:http://dev-meta-apollo.centaline.com.cn}           #DEV环境配置中心地址
#  autoUpdateInjectedSpringProperties: true   #是否开启 Spring 参数自动更新
#  bootstrap:
#    enabled: true                            #是否开启 Apollo
#    namespaces: application-dev.yml,application                #设置 Namespace
#    eagerLoad:
#      enabled: true                         #将 Apollo 加载提到初始化日志系统之前
