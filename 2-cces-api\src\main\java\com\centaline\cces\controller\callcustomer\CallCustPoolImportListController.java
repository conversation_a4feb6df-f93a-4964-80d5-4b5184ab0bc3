package com.centaline.cces.controller.callcustomer;

import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.callcustomer.CallCustPoolImportListService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Call客池管理
 */

@RestController
@RequestMapping("/callcustomer/callcustpoolimportlist")
public class CallCustPoolImportListController {
    @Resource
    CallCustPoolImportListService listService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@MultiRequestBody("type") String type) {

        return listService.getLayoutOfSearch(type);
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {


        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/getExportExcel"})
    public void getExportExcel(@RequestBody ModelParamQueryData paramDataQuery) {
        listService.getExportExcel(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute());
    }
}
