package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.core.redis.RedisCache;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;
import com.centaline.cces.service.commissionmanage.FinActualPaymentMonthSetListService;
import javax.annotation.Resource;


@RestController
@RequestMapping("/commissionmanage/finactualpaymentmonthsetlist")
public class FinActualPaymentMonthSetListController {
    @Resource
    FinActualPaymentMonthSetListService listService;

    @Resource
    RedisCache redisCache;

    @Resource
    ExcelAsyncTask asyncTask;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {


        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

}
