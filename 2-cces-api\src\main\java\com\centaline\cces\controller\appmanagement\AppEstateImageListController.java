package com.centaline.cces.controller.appmanagement;

import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.AppEstateImageListService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelMedia;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@RestController
@RequestMapping("/appmanagement/AppEstateImageList")
public class AppEstateImageListController {
    @Resource
    AppEstateImageListService listService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject
            , @MultiRequestBody("estateId") String estateId
            , @MultiRequestBody("estateName") String estateName) {
        return listService.getLayoutOfSearch(estateId,estateName);
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject, @RequestBody ModelParamQueryData paramDataQuery) {
        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/getFile"})
    public ReturnObject<List<ModelMedia>> getFile(
            @MultiRequestBody("roomTypeName") String roomTypeName
            , @MultiRequestBody("filePath") String mediaUrl) {

        return listService.getFile(roomTypeName, mediaUrl);
    }

}
