package com.centaline.cces.controller.cache;

import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.cache.CacheDevicesService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ReturnObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 人员设备
 */
@RestController
@RequestMapping("/system/device")
@Api(tags = "设备管理")
public class CacheDevicesController {

    @Resource
    CacheDevicesService service;

    /**
     * 移除单个设备
     *
     * @return
     */
    @ApiOperation("移除单个设备")
    @PostMapping({"/removeSingleDevice"})
    public ReturnObject<Object> removeSingleDevice() {

        service.removeDevice(false);
        return new ReturnObject<>();
    }

    /**
     * 移除单个设备
     *
     * @return
     */
    @ApiOperation("移除所有设备")
    @PostMapping({"/removeAllDevice"})
    public ReturnObject<Object> removeAllDevice() {

        service.removeAllDevices();
        return new ReturnObject<>();
    }

    /**
     * 移除单个设备
     *
     * @return
     */
    @ApiOperation("根据设备code移除")
    @PostMapping({"/removeDeviceByCode"})
    public ReturnObject<String> removeDeviceByCode(@MultiRequestBody("machineCode") String machineCode) {
        ReturnObject<String> returnObject = new ReturnObject<>();
        service.removeDeviceByCode(false, machineCode);
        return returnObject.setNotification(ActionType.Delete).setContent(machineCode);
    }

}
