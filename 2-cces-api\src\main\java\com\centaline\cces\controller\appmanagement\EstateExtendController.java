package com.centaline.cces.controller.appmanagement;


import com.centaline.cces.common.model.driver.ModelParamFormData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.appmanagement.EstateExtendService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/appmanagement/estateExtend")
public class EstateExtendController {

    @Resource
    EstateExtendService service;

    @PostMapping({"/readDetail"})
    public ReturnObject<ModelForm> readDetail(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("estateId") String estateId) {
        return service.readDetail(actionType, estateId);
    }

    @PostMapping({"/save"})
    public ReturnObject<Object> save(
            @MultiRequestBody("actionType") ActionType actionType,
            @MultiRequestBody("jsonData") ModelParamFormData formData) {

        return service.save(actionType, formData);
    }

    @PostMapping({"/updateParentEstateIdReadDetail"})
    public ReturnObject<ModelForm> updateParentEstateIdReadDetail(
            @MultiRequestBody("estateId") String estateId) {
        return service.updateParentEstateIdReadDetail(estateId);
    }

    @PostMapping({"/updateParentEstateId"})
    public ReturnObject<Object> updateParentEstateId(
            @MultiRequestBody("jsonData") ModelParamFormData formData) {

        return service.updateParentEstateId(formData);
    }
}
