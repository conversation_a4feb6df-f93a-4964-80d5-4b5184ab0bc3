package com.centaline.cces.controller.batchcontractadjust;

import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.framework.annotation.MultiRequestBody;
import com.centaline.cces.service.batchcontractadjust.BatchAdjustCommissionPriceDetailListService;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/batchcontractadjust/batchadjustcommissionPriceDetailList")
public class BatchAdjustCommissionPriceDetailListController {
    @Resource
    BatchAdjustCommissionPriceDetailListService listService;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@MultiRequestBody("commissionBatchID") String commissionBatchID) {


        return listService.getLayoutOfSearch(commissionBatchID);
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestBody ModelParamQueryData paramDataQuery) {

        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }


}
