package com.centaline.cces.controller.commissionmanage;

import com.centaline.cces.common.core.redis.CacheUtil;
import com.centaline.cces.common.dto.request.RequestAsyncExcel;
import com.centaline.cces.common.model.driver.AuthObject;
import com.centaline.cces.common.model.driver.ModelAsyncProcessResponse;
import com.centaline.cces.common.model.driver.ModelParamQueryData;
import com.centaline.cces.controller.asynctask.ExcelAsyncTask;
import com.centaline.cces.service.commissionmanage.ContractCommissionListService;
import com.centaline.drivermodel.enums.ActionType;
import com.centaline.drivermodel.models.ModelForm;
import com.centaline.drivermodel.models.ModelSearchPage;
import com.centaline.drivermodel.models.ReturnObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/commissionmanage/ContractCommissionInfo")
public class ContractCommissionListController {

    @Resource
    ContractCommissionListService listService;

    @Resource
    CacheUtil cacheUtil;

    @Resource
    ExcelAsyncTask task;

    @PostMapping({"/getLayoutOfSearch"})
    public ReturnObject<ModelForm> getLayoutOfSearch(@RequestHeader("authObject") AuthObject authObject) {


        return listService.getLayoutOfSearch();
    }

    @PostMapping({"/getList"})
    public ReturnObject<ModelSearchPage<Object>> getList(@RequestHeader("authObject") AuthObject authObject,
                                                         @RequestBody ModelParamQueryData paramDataQuery) {


        return listService.getList(paramDataQuery.getSearchFields(), paramDataQuery.getPageAttribute(), paramDataQuery.getFlagSearch());
    }

    @PostMapping({"/asyncExportExcel"})
    public ReturnObject<ModelAsyncProcessResponse> asyncExportExcel(@RequestHeader("authObject") AuthObject authObject,
                                                                    @RequestBody RequestAsyncExcel requestAsyncExcel) {
        ReturnObject<ModelAsyncProcessResponse> rtn = new ReturnObject<>();
        ModelAsyncProcessResponse exportAsyncRes = new ModelAsyncProcessResponse(cacheUtil);
        rtn.setContent(exportAsyncRes);
        rtn.setNotification(ActionType.None);
        //异步处理文档
        task.exportExcel(exportAsyncRes.getKey(), requestAsyncExcel, listService);

        return rtn;
    }
}
